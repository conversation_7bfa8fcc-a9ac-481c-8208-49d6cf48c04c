import { ConfigProvider, theme, Button } from 'antd';
import { StyleProvider } from '@ant-design/cssinjs';
import './App.css';

function App() {
  return (
    <ConfigProvider
      theme={{
        // 1. 单独使用暗色算法
        algorithm: theme.darkAlgorithm,

        // 2. 同时使用暗色算法和紧凑算法
        // algorithm: [theme.darkAlgorithm, theme.compactAlgorithm],
      }}
    >
      <StyleProvider hashPriority="high">
        <div className="h-screen bg-gray-800 flex flex-col justify-center items-center">
          <h1 className="text-3xl font-bold text-white mb-8">
            Ant Design + Tailwind CSS
          </h1>
          <Button type="primary">这是一个 AntD 按钮</Button>
          <div className="mt-4 p-4 bg-blue-500 text-white rounded-md">
            这是一个 Tailwind 样式的 div
          </div>
        </div>
      </StyleProvider>
    </ConfigProvider>
  );
}

export default App;
