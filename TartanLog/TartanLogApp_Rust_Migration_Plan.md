# TartanLogApp Rust + Tauri 迁移计划

## 项目概述

将现有的 WPF C# 达坦实时Gamma成像系统迁移到 Rust + Tauri 架构，实现跨平台支持和现代化的用户界面。

## 当前项目分析

### 核心功能模块
- **实时数据采集与处理**: 串口通信、TCP网络通信、WITS协议数据处理
- **数据存储与管理**: SQLite数据库、LAS文件处理、Excel导出
- **实时图表与可视化**: Gamma曲线显示、实时成像显示
- **网络数据传输**: 数据上传到云端、实时数据远传

### 技术栈对比
| 功能 | 当前技术 | 目标技术 |
|------|----------|----------|
| 前端 | WPF (XAML + C#) | React/Vue + TypeScript |
| 后端 | C# .NET Framework | Rust |
| 数据库 | SQLite + Entity Framework | SQLite + rusqlite |
| 通信 | System.IO.Ports + TcpClient | serialport + tokio |
| 图表 | WPF Canvas 自定义绘制 | Chart.js/D3.js/ECharts |

## 迁移任务列表

### 🚀 第一阶段：基础设施 (优先级：最高)

#### 1. 项目初始化和基础架构
- [ ] 创建新的Tauri项目
- [ ] 设置Rust后端和前端框架 (React + TypeScript)
- [ ] 配置开发环境和构建工具
- [ ] 设置基础的目录结构
```
tartan-log-tauri/
├── src-tauri/          # Rust后端
│   ├── src/
│   │   ├── main.rs
│   │   ├── commands/   # Tauri命令
│   │   ├── models/     # 数据模型
│   │   ├── services/   # 业务服务
│   │   └── utils/      # 工具函数
│   └── Cargo.toml
├── src/                # 前端 (React)
│   ├── components/
│   ├── pages/
│   └── utils/
└── package.json
```

#### 2. 核心数据模型迁移
- [ ] 将C#的数据结构转换为Rust的struct
- [ ] 实现序列化/反序列化 (serde)
- [ ] 定义核心业务实体：
  - `WITSEntity` -> `WitsEntity`
  - `LoggingData` -> `LoggingData`
  - `TransItem` -> `TransItem`
  - `SerialMessage` -> `SerialMessage`

#### 3. 数据库访问层实现
- [ ] 使用rusqlite替代Entity Framework
- [ ] 实现基础的CRUD操作
- [ ] 数据库连接池和事务管理
- [ ] 迁移MeztlDB的核心功能

### 🔧 第二阶段：核心通信功能 (优先级：高)

#### 4. 串口通信功能
- [ ] 使用serialport crate
- [ ] 实现异步串口读写
- [ ] 错误处理和重连机制
- [ ] 迁移TartanSerialPort功能

#### 5. 基础前端界面
- [ ] 实现主窗口布局
- [ ] 创建基础UI组件
- [ ] 设置状态管理 (Redux/Zustand)
- [ ] 实现响应式设计

#### 6. Tauri命令接口
- [ ] 定义前后端通信接口
- [ ] 实现基础的命令处理
- [ ] 事件系统设置
- [ ] 错误处理和类型安全

### 🌐 第三阶段：网络和协议 (优先级：中高)

#### 7. TCP网络通信
- [ ] 实现TCP客户端和服务器
- [ ] 异步网络处理 (tokio)
- [ ] 连接管理和心跳机制
- [ ] 迁移NetConnectService和NetListenerService

#### 8. WITS协议处理
- [ ] 协议解析和生成
- [ ] 数据格式转换
- [ ] 协议验证
- [ ] 错误处理

### 📊 第四阶段：数据处理和显示 (优先级：中)

#### 9. 实时数据处理
- [ ] 异步数据采集任务
- [ ] 数据缓存和队列
- [ ] 实时数据流处理
- [ ] 性能优化

#### 10. 基础图表显示
- [ ] 集成Chart.js或类似库
- [ ] 实现基础曲线显示
- [ ] 实时数据更新
- [ ] 图表交互功能

### 📁 第五阶段：文件和高级功能 (优先级：中低)

#### 11. 文件处理功能
- [ ] LAS文件读写
- [ ] Excel导出功能
- [ ] 文件格式转换
- [ ] 文件系统操作

#### 12. 高级图表功能
- [ ] 图表缩放和平移
- [ ] 多系列数据显示
- [ ] 图表配置和导出
- [ ] 自定义图表样式

### 🎨 第六阶段：专业功能 (优先级：低)

#### 13. Gamma成像显示
- [ ] 成像数据可视化
- [ ] 扇区显示
- [ ] 成像参数配置
- [ ] 3D可视化

#### 14. 数据远传功能
- [ ] 云端数据上传
- [ ] API集成
- [ ] 数据同步
- [ ] 离线缓存

### ⚙️ 第七阶段：完善和优化 (优先级：最低)

#### 15. 用户设置和配置
- [ ] 配置文件管理
- [ ] 用户偏好设置
- [ ] 主题和界面定制
- [ ] 多语言支持

#### 16. 测试和优化
- [ ] 单元测试
- [ ] 集成测试
- [ ] 性能优化
- [ ] 错误处理完善
- [ ] 文档编写

## 技术选型

### Rust 后端依赖
```toml
[dependencies]
tauri = { version = "1.0", features = ["api-all"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tokio = { version = "1.0", features = ["full"] }
rusqlite = { version = "0.29", features = ["bundled"] }
serialport = "4.2"
anyhow = "1.0"
thiserror = "1.0"
chrono = { version = "0.4", features = ["serde"] }
uuid = { version = "1.0", features = ["v4", "serde"] }
```

### 前端技术栈
- **框架**: React 18 + TypeScript
- **状态管理**: Zustand 或 Redux Toolkit
- **UI库**: Ant Design 或 Material-UI
- **图表库**: Chart.js 或 ECharts
- **构建工具**: Vite
- **样式**: Tailwind CSS

## 开发里程碑

### 里程碑 1: 基础架构完成 (2-3周)
- 项目初始化
- 数据模型定义
- 数据库访问层

### 里程碑 2: 核心通信功能 (3-4周)
- 串口通信
- 基础UI界面
- Tauri命令接口

### 里程碑 3: 网络功能完成 (2-3周)
- TCP通信
- WITS协议处理

### 里程碑 4: 数据可视化 (3-4周)
- 实时数据处理
- 基础图表显示

### 里程碑 5: 完整功能 (4-6周)
- 文件处理
- 高级图表功能
- 专业功能实现

## 风险评估

### 高风险项
- **实时图表性能**: Web技术栈的实时绘制性能可能不如原生WPF
- **串口通信稳定性**: 跨平台串口通信的兼容性问题
- **复杂UI迁移**: 从WPF到Web的UI范式转换

### 缓解策略
- 使用WebGL加速图表渲染
- 充分测试不同平台的串口功能
- 采用组件化设计，逐步迁移UI功能

## 下一步行动

1. **立即开始**: 项目初始化和基础架构
2. **并行开发**: 数据模型迁移和数据库访问层
3. **迭代验证**: 每个阶段完成后进行功能验证
4. **持续集成**: 建立自动化测试和部署流程

---

*本文档将随着项目进展持续更新*
